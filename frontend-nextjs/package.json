{"name": "frontend-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "15.3.3", "numeral": "^2.0.6", "postcss": "^8.5.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/numeral": "^2.0.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^9", "eslint-config-next": "15.3.3", "prettier": "^3.1.0", "typescript": "^5"}}